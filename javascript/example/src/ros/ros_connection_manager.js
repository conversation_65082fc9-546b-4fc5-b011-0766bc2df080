/**
 * ROS 連接管理器 - 增強版
 * 專責 WebSocket 連接的建立、維護和斷開
 *
 * 新增功能：
 * - 跨模式連接持久化
 * - 智能重連機制
 * - 連接健康檢查
 * - 模式切換時保持連接
 */

import { rosStateManager } from './ros_state_manager.js';
import { Logger } from '../shared/utils.js';

class ROSConnectionManager {
    constructor() {
        this.ros = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10; // 增加重連次數
        this.reconnectDelay = 1500; // 減少重連延遲
        this.reconnectTimer = null;
        this.connectionTimeout = 8000; // 減少連接超時

        // 新增：連接持久化配置
        this.persistentConnection = true; // 跨模式保持連接
        this.healthCheckInterval = null;
        this.healthCheckDelay = 30000; // 30秒健康檢查
        this.lastPingTime = null;
        this.connectionQuality = 'unknown'; // 'good', 'poor', 'unknown'

        // 新增：模式切換保護
        this.modeSwitchProtection = true;
        this.switchingMode = false;

        // 綁定方法
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.reconnect = this.reconnect.bind(this);
        this.forceDisconnect = this.forceDisconnect.bind(this);
        this.startHealthCheck = this.startHealthCheck.bind(this);
        this.stopHealthCheck = this.stopHealthCheck.bind(this);

        Logger.info('ROSConnectionManager', '增強版連接管理器已初始化');
    }
    
    /**
     * 設置模式切換保護
     * @param {boolean} switching - 是否正在切換模式
     */
    setModeSwitching(switching) {
        this.switchingMode = switching;
        if (switching) {
            Logger.info('ROSConnectionManager', '模式切換保護已啟用');
        } else {
            Logger.info('ROSConnectionManager', '模式切換保護已關閉');
        }
    }

    /**
     * 強制斷開連接（忽略持久化設置）
     */
    forceDisconnect() {
        const wasPersistent = this.persistentConnection;
        this.persistentConnection = false;
        this.disconnect();
        this.persistentConnection = wasPersistent;
        Logger.info('ROSConnectionManager', '強制斷開連接');
    }

    /**
     * 連接到 ROS Bridge - 增強版
     * @param {string} url - WebSocket URL
     * @param {Object} options - 連接選項
     * @returns {Promise<Object>} ROS 實例
     */
    async connect(url, options = {}) {
        // 如果已經在連接中，返回現有的連接
        if (rosStateManager.getState('connecting')) {
            Logger.info('ROSConnectionManager', '連接進行中，等待完成...');
            return this.waitForConnection();
        }

        // 如果已經連接，直接返回
        if (rosStateManager.getState('connected') && this.ros) {
            Logger.info('ROSConnectionManager', '已連接，返回現有連接');
            return this.ros;
        }
        
        const {
            autoReconnect = true,
            timeout = this.connectionTimeout
        } = options;
        
        // 設置連接中狀態
        rosStateManager.setState({
            connecting: true,
            error: null,
            url
        });
        
        try {
            console.log(`🔄 正在連接到 ROS Bridge: ${url}`);
            
            // 創建 ROS 實例
            this.ros = new window.ROSLIB.Ros({ url });
            
            // 設置連接超時
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`連接超時: ${url}`));
                }, timeout);
            });
            
            // 等待連接建立
            const connectionPromise = new Promise((resolve, reject) => {
                // 連接成功事件
                this.ros.on('connection', () => {
                    this.onConnectionSuccess(url, autoReconnect);
                    resolve(this.ros);
                });
                
                // 連接錯誤事件
                this.ros.on('error', (error) => {
                    this.onConnectionError(error, autoReconnect);
                    reject(error);
                });
                
                // 連接關閉事件
                this.ros.on('close', () => {
                    this.onConnectionClose(autoReconnect);
                });
            });
            
            // 等待連接完成或超時
            const result = await Promise.race([connectionPromise, timeoutPromise]);
            
            return result;
            
        } catch (error) {
            this.handleConnectionFailure(error);
            throw error;
        }
    }
    
    /**
     * 斷開 ROS 連接 - 增強版
     * @param {boolean} force - 是否強制斷開（忽略持久化設置）
     */
    disconnect(force = false) {
        // 模式切換保護：如果正在切換模式且啟用持久化，則不斷開連接
        if (this.switchingMode && this.persistentConnection && !force) {
            Logger.info('ROSConnectionManager', '模式切換中，保持連接');
            return;
        }

        // 持久化保護：如果啟用持久化且非強制斷開，則不斷開連接
        if (this.persistentConnection && !force) {
            Logger.info('ROSConnectionManager', '持久化連接已啟用，保持連接');
            return;
        }

        Logger.info('ROSConnectionManager', '正在斷開 ROS 連接...');

        // 停止健康檢查
        this.stopHealthCheck();

        // 清除重連計時器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        // 關閉連接
        if (this.ros) {
            try {
                this.ros.close();
            } catch (error) {
                Logger.warning('ROSConnectionManager', '關閉連接時發生錯誤', error);
            }
            this.ros = null;
        }

        // 重置狀態
        this.reconnectAttempts = 0;
        this.connectionQuality = 'unknown';
        rosStateManager.setState({
            connected: false,
            connecting: false,
            url: null,
            error: null
        });

        Logger.info('ROSConnectionManager', 'ROS 連接已斷開');
    }
    
    /**
     * 重新連接
     */
    async reconnect() {
        const url = rosStateManager.getState('url');
        if (!url) {
            console.error('❌ 無法重連：沒有保存的 URL');
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`🔄 嘗試重新連接 (${this.reconnectAttempts}/${this.maxReconnectAttempts}): ${url}`);
        
        try {
            await this.connect(url, { autoReconnect: true });
        } catch (error) {
            console.error(`❌ 重連失敗 (${this.reconnectAttempts}/${this.maxReconnectAttempts}):`, error);
            
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            } else {
                console.error('❌ 達到最大重連次數，停止重連');
                rosStateManager.recordError(new Error('達到最大重連次數'), 'RECONNECT_FAILED');
            }
        }
    }
    
    /**
     * 安排重新連接
     */
    scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指數退避
        console.log(`⏰ ${delay}ms 後嘗試重新連接...`);
        
        this.reconnectTimer = setTimeout(() => {
            this.reconnect();
        }, delay);
    }
    
    /**
     * 等待連接完成
     * @returns {Promise<Object>} ROS 實例
     */
    waitForConnection() {
        return new Promise((resolve, reject) => {
            const checkConnection = () => {
                if (rosStateManager.getState('connected') && this.ros) {
                    resolve(this.ros);
                } else if (!rosStateManager.getState('connecting')) {
                    reject(new Error('連接失敗'));
                } else {
                    setTimeout(checkConnection, 100);
                }
            };
            checkConnection();
        });
    }
    
    /**
     * 連接成功處理 - 增強版
     * @param {string} url - 連接 URL
     * @param {boolean} autoReconnect - 是否自動重連
     */
    onConnectionSuccess(url, autoReconnect) {
        this.reconnectAttempts = 0;
        this.connectionQuality = 'good';
        this.lastPingTime = Date.now();

        rosStateManager.setState({
            connected: true,
            connecting: false,
            error: null,
            url
        });

        Logger.info('ROSConnectionManager', `已連接到 ROS Bridge: ${url}`);

        // 啟動健康檢查
        this.startHealthCheck();

        // 根據當前模式啟用對應功能
        this.activateModeFeatures();
    }
    
    /**
     * 連接錯誤處理
     * @param {Error} error - 錯誤對象
     * @param {boolean} autoReconnect - 是否自動重連
     */
    onConnectionError(error, autoReconnect) {
        console.error('❌ ROS 連接錯誤:', error);
        
        rosStateManager.setState({
            connected: false,
            connecting: false,
            error: error.message
        });
        

        
        // 如果啟用自動重連且未達到最大次數
        if (autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 連接關閉處理
     * @param {boolean} autoReconnect - 是否自動重連
     */
    onConnectionClose(autoReconnect) {
        console.warn('⚠️ ROS 連接已關閉');
        
        rosStateManager.setState({
            connected: false,
            connecting: false
        });
        
        // 如果是意外斷開且啟用自動重連
        if (autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 連接失敗處理
     * @param {Error} error - 錯誤對象
     */
    handleConnectionFailure(error) {
        rosStateManager.setState({
            connected: false,
            connecting: false,
            error: error.message
        });
        

    }
    
    /**
     * 根據當前模式啟用對應功能
     */
    activateModeFeatures() {
        const mode = rosStateManager.getState('mode');
        
        // 觸發模式啟用事件，讓其他管理器響應
        rosStateManager.notifyListeners('connectionReady', mode);
        
        console.log(`🎯 已啟用 ${mode} 模式功能`);
    }
    
    /**
     * 獲取連接狀態
     * @returns {Object} 連接狀態信息
     */
    getConnectionInfo() {
        return {
            connected: rosStateManager.getState('connected'),
            connecting: rosStateManager.getState('connecting'),
            url: rosStateManager.getState('url'),
            error: rosStateManager.getState('error'),
            reconnectAttempts: this.reconnectAttempts,
            maxReconnectAttempts: this.maxReconnectAttempts,
            hasRosInstance: !!this.ros
        };
    }
    
    /**
     * 獲取 ROS 實例
     * @returns {Object|null} ROS 實例
     */
    getROS() {
        return this.ros;
    }
    
    /**
     * 檢查是否已連接
     * @returns {boolean} 連接狀態
     */
    isConnected() {
        return rosStateManager.getState('connected') && !!this.ros;
    }

    /**
     * 啟動健康檢查
     */
    startHealthCheck() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }

        this.healthCheckInterval = setInterval(() => {
            this.performHealthCheck();
        }, this.healthCheckDelay);

        Logger.info('ROSConnectionManager', '健康檢查已啟動');
    }

    /**
     * 停止健康檢查
     */
    stopHealthCheck() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
            Logger.info('ROSConnectionManager', '健康檢查已停止');
        }
    }

    /**
     * 執行健康檢查
     */
    performHealthCheck() {
        if (!this.ros || !this.isConnected()) {
            return;
        }

        const now = Date.now();
        const timeSinceLastPing = now - (this.lastPingTime || 0);

        // 如果超過 60 秒沒有活動，發送 ping
        if (timeSinceLastPing > 60000) {
            try {
                // 嘗試獲取 ROS 參數來測試連接
                const param = new window.ROSLIB.Param({
                    ros: this.ros,
                    name: '/rosdistro'
                });

                param.get((value) => {
                    this.lastPingTime = now;
                    this.connectionQuality = 'good';
                    Logger.debug('ROSConnectionManager', '健康檢查通過');
                }, (error) => {
                    this.connectionQuality = 'poor';
                    Logger.warning('ROSConnectionManager', '健康檢查失敗', error);
                });
            } catch (error) {
                this.connectionQuality = 'poor';
                Logger.warning('ROSConnectionManager', '健康檢查異常', error);
            }
        }
    }

    /**
     * 獲取連接品質
     * @returns {string} 連接品質 ('good', 'poor', 'unknown')
     */
    getConnectionQuality() {
        return this.connectionQuality;
    }
}

/**
 * 獲取預設的 ROS Bridge URL
 * @param {number} port - 端口號，預設 9090
 * @returns {string} WebSocket URL
 */
export function getDefaultRosbridgeUrl(port = 9090) {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    return `${protocol}//${window.location.hostname}:${port}`;
}

// 創建全域實例
export const rosConnectionManager = new ROSConnectionManager();

// 導出類別供測試使用
export { ROSConnectionManager };
