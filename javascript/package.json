{"name": "urdf-loader", "version": "0.12.0", "description": "URDF Loader for THREE.js and webcomponent viewer", "main": "src/URDFLoader.js", "type": "module", "scripts": {"start": "concurrently \"parcel watch ./example/*.html --out-dir ./example/dev-bundle/ --public-url . --no-cache\" \"cd .. && static-server\"", "build": "rollup -c", "build-examples": "parcel build ./example/*.html --out-dir ./example/bundle/ --public-url . --no-cache --no-source-maps --no-content-hash", "test": "jest", "lint": "eslint \"./src/*.js\" \"./test/*.js\"", "prepublishOnly": "npm run build"}, "files": ["src/*", "umd/*"], "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/Chungchiyu/DancingTest.git"}, "bugs": {"url": "https://github.com/Chungchiyu/DancingTest/issues"}, "homepage": "https://github.com/Chungchiyu/DancingTest#readme", "keywords": ["javascript", "threejs", "graphics", "ros", "robotics", "urdf", "urdf-models", "webcomponents"], "peerDependencies": {"three": ">=0.152.0"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "@webcomponents/webcomponentsjs": "^2.4.4", "babel-jest": "^29.5.0", "caniuse-lite": "^1.0.30001727", "concurrently": "^6.2.1", "eslint": "^7.10.0", "eslint-plugin-jest": "^24.1.0", "jest": "^27.1.1", "jest-cli": "^27.1.1", "jsdom": "^17.0.0", "nyc": "^15.1.0", "parcel-bundler": "^1.12.5", "rollup": "^2.29.0", "static-server": "^3.0.0", "three": "^0.152.2"}, "dependencies": {"@tensorflow-models/pose-detection": "^2.1.3", "@tensorflow-models/posenet": "^2.2.2", "@tensorflow/tfjs": "^4.20.0", "@tensorflow/tfjs-core": "^3.21.0", "install": "^0.13.0", "mathjs": "^12.4.2", "roslib": "^1.4.1", "sortablejs": "^1.15.2", "video.js": "^8.12.0"}}